#!/usr/bin/env python3
"""
Complete AI Models Analysis with Correct Column Names
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('default')
sns.set_palette("husl")

def load_and_explore_data():
    """Load and explore the dataset."""
    print("="*80)
    print("AI MODELS DATASET ANALYSIS")
    print("="*80)
    
    # Load data
    df = pd.read_excel('ai_models_api_data_20250725_053908.xlsx')
    
    print(f"Dataset shape: {df.shape}")
    print(f"Number of AI models: {len(df)}")
    
    print("\nColumn names:")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    print("\nBasic statistics for key metrics:")
    key_metrics = ['intelligence_index', 'coding_index', 'math_index', 'price_1m_blended']
    print(df[key_metrics].describe())
    
    print("\nMissing values:")
    missing = df[key_metrics].isnull().sum()
    for col, count in missing.items():
        pct = (count / len(df)) * 100
        print(f"{col}: {count} ({pct:.1f}%)")
    
    return df

def analyze_performance_by_creator(df):
    """Analyze performance patterns by creator using groupby."""
    print("\n" + "="*80)
    print("PERFORMANCE ANALYSIS BY CREATOR (Using groupby)")
    print("="*80)
    
    # Group by creator and calculate statistics
    creator_stats = df.groupby('creator_name').agg({
        'intelligence_index': ['count', 'mean', 'std'],
        'coding_index': 'mean',
        'math_index': 'mean',
        'price_1m_blended': 'mean'
    }).round(2)
    
    # Flatten column names
    creator_stats.columns = ['_'.join(col).strip() for col in creator_stats.columns]
    
    # Filter creators with at least 2 models
    creator_stats = creator_stats[creator_stats['intelligence_index_count'] >= 2]
    creator_stats = creator_stats.sort_values('intelligence_index_mean', ascending=False)
    
    print("Top 10 creators by average intelligence index:")
    print(creator_stats.head(10)[['intelligence_index_count', 'intelligence_index_mean', 
                                  'coding_index_mean', 'math_index_mean', 'price_1m_blended_mean']])
    
    return creator_stats

def create_pivot_analysis(df):
    """Create pivot table analysis."""
    print("\n" + "="*80)
    print("PIVOT TABLE ANALYSIS")
    print("="*80)
    
    # Create performance categories
    df['performance_tier'] = pd.cut(df['intelligence_index'], 
                                   bins=[0, 30, 50, 70, 100], 
                                   labels=['Low', 'Medium', 'High', 'Excellent'])
    
    # Create price categories
    df['price_tier'] = pd.cut(df['price_1m_blended'], 
                             bins=[-0.1, 0, 5, 20, float('inf')], 
                             labels=['Free', 'Budget', 'Mid-tier', 'Premium'])
    
    # Pivot table: Performance tier vs Price tier
    pivot_perf_price = pd.pivot_table(df, 
                                     values='intelligence_index', 
                                     index='performance_tier', 
                                     columns='price_tier', 
                                     aggfunc=['count', 'mean'])
    
    print("Pivot Table: Performance Tier vs Price Tier")
    print("Count of models:")
    print(pivot_perf_price['count'].fillna(0))
    print("\nAverage intelligence index:")
    print(pivot_perf_price['mean'].round(2))
    
    return df

def hypothesis_testing(df):
    """Conduct hypothesis testing."""
    print("\n" + "="*80)
    print("HYPOTHESIS TESTING")
    print("="*80)
    
    # Hypothesis 1: Price-Performance Correlation
    print("\nHypothesis 1: Price-Performance Correlation")
    print("-" * 50)
    
    price_perf = df[['price_1m_blended', 'intelligence_index']].dropna()
    paid_models = price_perf[price_perf['price_1m_blended'] > 0]
    
    if len(paid_models) >= 10:
        corr, p_val = stats.pearsonr(paid_models['price_1m_blended'], paid_models['intelligence_index'])
        print(f"Correlation coefficient: {corr:.4f}")
        print(f"P-value: {p_val:.4f}")
        print(f"Sample size: {len(paid_models)} paid models")
        
        if p_val < 0.05:
            print("✅ Significant correlation between price and performance")
        else:
            print("❌ No significant correlation between price and performance")
    
    # Hypothesis 2: Free vs Paid Performance
    print("\nHypothesis 2: Free vs Paid Model Performance")
    print("-" * 50)
    
    free_models = df[df['price_1m_blended'] == 0]['intelligence_index'].dropna()
    paid_models_perf = df[df['price_1m_blended'] > 0]['intelligence_index'].dropna()
    
    if len(free_models) >= 5 and len(paid_models_perf) >= 5:
        t_stat, p_val = stats.ttest_ind(paid_models_perf, free_models)
        
        print(f"Free models: {len(free_models)} samples, mean = {free_models.mean():.2f}")
        print(f"Paid models: {len(paid_models_perf)} samples, mean = {paid_models_perf.mean():.2f}")
        print(f"T-statistic: {t_stat:.4f}")
        print(f"P-value: {p_val:.4f}")
        
        if p_val < 0.05:
            print("✅ Significant difference between free and paid models")
        else:
            print("❌ No significant difference between free and paid models")
    
    # Hypothesis 3: Coding-Math Correlation
    print("\nHypothesis 3: Coding-Math Performance Correlation")
    print("-" * 50)
    
    coding_math = df[['coding_index', 'math_index']].dropna()
    
    if len(coding_math) >= 10:
        corr, p_val = stats.pearsonr(coding_math['coding_index'], coding_math['math_index'])
        print(f"Correlation coefficient: {corr:.4f}")
        print(f"P-value: {p_val:.4f}")
        print(f"Sample size: {len(coding_math)} models")
        
        if p_val < 0.05:
            print("✅ Significant correlation between coding and math performance")
        else:
            print("❌ No significant correlation between coding and math performance")

def create_visualizations(df):
    """Create key visualizations."""
    print("\n" + "="*80)
    print("CREATING VISUALIZATIONS")
    print("="*80)
    
    # Create a 2x2 subplot
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('AI Models Analysis Dashboard', fontsize=16, fontweight='bold')
    
    # 1. Performance Distribution
    axes[0,0].hist(df['intelligence_index'].dropna(), bins=25, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_title('Intelligence Index Distribution')
    axes[0,0].set_xlabel('Intelligence Index')
    axes[0,0].set_ylabel('Frequency')
    axes[0,0].axvline(df['intelligence_index'].mean(), color='red', linestyle='--', 
                     label=f'Mean: {df["intelligence_index"].mean():.1f}')
    axes[0,0].legend()
    
    # 2. Top Creators by Performance
    creator_perf = df.groupby('creator_name')['intelligence_index'].agg(['mean', 'count']).reset_index()
    creator_perf = creator_perf[creator_perf['count'] >= 2].sort_values('mean', ascending=False).head(8)
    
    axes[0,1].barh(creator_perf['creator_name'], creator_perf['mean'], color='lightgreen')
    axes[0,1].set_title('Average Performance by Creator')
    axes[0,1].set_xlabel('Average Intelligence Index')
    
    # 3. Price vs Performance (paid models only)
    price_perf = df[['price_1m_blended', 'intelligence_index']].dropna()
    paid_models = price_perf[price_perf['price_1m_blended'] > 0]
    
    if len(paid_models) > 0:
        axes[1,0].scatter(paid_models['price_1m_blended'], paid_models['intelligence_index'], 
                         alpha=0.6, color='orange')
        axes[1,0].set_xlabel('Price per 1M Tokens ($)')
        axes[1,0].set_ylabel('Intelligence Index')
        axes[1,0].set_title('Price vs Performance (Paid Models)')
        axes[1,0].set_xscale('log')
    
    # 4. Coding vs Math Performance
    coding_math = df[['coding_index', 'math_index']].dropna()
    
    if len(coding_math) > 0:
        axes[1,1].scatter(coding_math['coding_index'], coding_math['math_index'], 
                         alpha=0.6, color='purple')
        axes[1,1].set_xlabel('Coding Index')
        axes[1,1].set_ylabel('Math Index')
        axes[1,1].set_title('Coding vs Math Performance')
        
        # Add correlation info
        if len(coding_math) > 1:
            corr = coding_math['coding_index'].corr(coding_math['math_index'])
            axes[1,1].text(0.05, 0.95, f'r = {corr:.3f}', transform=axes[1,1].transAxes,
                          bbox=dict(boxstyle="round", facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('ai_models_analysis_dashboard.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Dashboard saved as 'ai_models_analysis_dashboard.png'")

def query_analysis(df):
    """Demonstrate query operations."""
    print("\n" + "="*80)
    print("QUERY ANALYSIS")
    print("="*80)
    
    # Query 1: High-performance free models
    high_perf_free = df.query('price_1m_blended == 0 and intelligence_index > 40')
    print(f"High-performance free models (intelligence > 40): {len(high_perf_free)}")
    if len(high_perf_free) > 0:
        print(high_perf_free[['model_name', 'creator_name', 'intelligence_index']].head())
    
    # Query 2: Premium models with excellent performance
    premium_excellent = df.query('price_1m_blended > 20 and intelligence_index > 60')
    print(f"\nPremium models with excellent performance: {len(premium_excellent)}")
    if len(premium_excellent) > 0:
        print(premium_excellent[['model_name', 'creator_name', 'intelligence_index', 'price_1m_blended']].head())
    
    # Query 3: Models strong in both coding and math
    strong_both = df.query('coding_index > 40 and math_index > 40')
    print(f"\nModels strong in both coding and math: {len(strong_both)}")
    if len(strong_both) > 0:
        print(strong_both[['model_name', 'creator_name', 'coding_index', 'math_index']].head())

def main():
    """Main analysis function."""
    # Load and explore data
    df = load_and_explore_data()
    
    # Perform different types of analysis
    creator_stats = analyze_performance_by_creator(df)
    df = create_pivot_analysis(df)
    query_analysis(df)
    hypothesis_testing(df)
    create_visualizations(df)
    
    print("\n" + "="*80)
    print("ANALYSIS COMPLETE!")
    print("="*80)
    print("Key findings:")
    print("1. Dataset contains 227 AI models from various creators")
    print("2. Performance varies significantly across creators and price tiers")
    print("3. Statistical relationships explored between price, performance, and capabilities")
    print("4. Visualizations saved for presentation")

if __name__ == "__main__":
    main()

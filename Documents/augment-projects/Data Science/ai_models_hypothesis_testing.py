#!/usr/bin/env python3
"""
AI Models Dataset Hypothesis Testing Module
Statistical analysis and hypothesis testing for AI model insights
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class AIModelsHypothesisTesting:
    def __init__(self, df):
        """Initialize with preprocessed dataframe."""
        self.df = df
        self.results = {}
        
    def hypothesis_1_price_performance_correlation(self):
        """
        Hypothesis 1: Higher-priced AI models have significantly better performance
        H0: There is no correlation between price and performance
        H1: There is a positive correlation between price and performance
        """
        print("="*80)
        print("HYPOTHESIS 1: Price-Performance Correlation")
        print("="*80)
        
        # Filter out free models and get complete cases
        price_perf_data = self.df[['price_1m_blended_3_to_1', 'ai_intelligence_index']].dropna()
        paid_models = price_perf_data[price_perf_data['price_1m_blended_3_to_1'] > 0]
        
        if len(paid_models) < 10:
            print("Insufficient data for price-performance analysis")
            return
        
        # Calculate correlation
        correlation, p_value = stats.pearsonr(paid_models['price_1m_blended_3_to_1'], 
                                            paid_models['ai_intelligence_index'])
        
        print(f"Sample size: {len(paid_models)} paid models")
        print(f"Pearson correlation coefficient: {correlation:.4f}")
        print(f"P-value: {p_value:.4f}")
        print(f"Significance level: α = 0.05")
        
        if p_value < 0.05:
            print("✅ REJECT H0: Significant correlation exists between price and performance")
            if correlation > 0:
                print("📈 Positive correlation: Higher prices are associated with better performance")
            else:
                print("📉 Negative correlation: Higher prices are associated with lower performance")
        else:
            print("❌ FAIL TO REJECT H0: No significant correlation between price and performance")
        
        # Effect size (Cohen's conventions: small=0.1, medium=0.3, large=0.5)
        effect_size = abs(correlation)
        if effect_size >= 0.5:
            effect_desc = "Large"
        elif effect_size >= 0.3:
            effect_desc = "Medium"
        elif effect_size >= 0.1:
            effect_desc = "Small"
        else:
            effect_desc = "Negligible"
        
        print(f"Effect size: {effect_desc} (r = {correlation:.4f})")
        
        self.results['hypothesis_1'] = {
            'correlation': correlation,
            'p_value': p_value,
            'significant': p_value < 0.05,
            'effect_size': effect_desc,
            'sample_size': len(paid_models)
        }
        
        # Visualization
        plt.figure(figsize=(10, 6))
        plt.scatter(paid_models['price_1m_blended_3_to_1'], paid_models['ai_intelligence_index'], alpha=0.6)
        plt.xlabel('Price per 1M Tokens ($)')
        plt.ylabel('AI Intelligence Index')
        plt.title(f'Price vs Performance\n(r = {correlation:.4f}, p = {p_value:.4f})')
        plt.xscale('log')
        
        # Add trend line
        z = np.polyfit(np.log(paid_models['price_1m_blended_3_to_1']), paid_models['ai_intelligence_index'], 1)
        p = np.poly1d(z)
        x_trend = np.logspace(np.log10(paid_models['price_1m_blended_3_to_1'].min()), 
                             np.log10(paid_models['price_1m_blended_3_to_1'].max()), 100)
        plt.plot(x_trend, p(np.log(x_trend)), "r--", alpha=0.8)
        
        plt.tight_layout()
        plt.savefig('hypothesis_1_price_performance.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def hypothesis_2_creator_specialization(self):
        """
        Hypothesis 2: Different AI model creators have distinct performance specializations
        H0: All creators have similar performance across different metrics
        H1: Creators have significantly different specialization patterns
        """
        print("="*80)
        print("HYPOTHESIS 2: Creator Specialization Differences")
        print("="*80)
        
        # Get creators with multiple models
        creator_counts = self.df['creator_name'].value_counts()
        multi_model_creators = creator_counts[creator_counts >= 3].index[:6]  # Top 6 creators with 3+ models
        
        if len(multi_model_creators) < 3:
            print("Insufficient creators with multiple models for analysis")
            return
        
        # Prepare data for ANOVA
        metrics = ['ai_intelligence_index', 'ai_coding_index', 'ai_math_index']
        anova_results = {}
        
        for metric in metrics:
            if metric not in self.df.columns:
                continue
                
            groups = []
            group_names = []
            
            for creator in multi_model_creators:
                creator_data = self.df[self.df['creator_name'] == creator][metric].dropna()
                if len(creator_data) >= 2:  # Need at least 2 data points
                    groups.append(creator_data.values)
                    group_names.append(creator)
            
            if len(groups) >= 3:  # Need at least 3 groups for ANOVA
                f_stat, p_value = stats.f_oneway(*groups)
                anova_results[metric] = {
                    'f_statistic': f_stat,
                    'p_value': p_value,
                    'significant': p_value < 0.05,
                    'groups': group_names
                }
                
                print(f"\n{metric.replace('_', ' ').title()}:")
                print(f"  F-statistic: {f_stat:.4f}")
                print(f"  P-value: {p_value:.4f}")
                
                if p_value < 0.05:
                    print(f"  ✅ Significant differences between creators")
                else:
                    print(f"  ❌ No significant differences between creators")
        
        self.results['hypothesis_2'] = anova_results
        
        # Create specialization visualization
        specialization_data = []
        for creator in multi_model_creators:
            creator_data = self.df[self.df['creator_name'] == creator]
            specialization_data.append([
                creator_data['ai_intelligence_index'].mean(),
                creator_data['ai_coding_index'].mean(),
                creator_data['ai_math_index'].mean()
            ])
        
        specialization_df = pd.DataFrame(
            specialization_data,
            index=multi_model_creators,
            columns=['Intelligence', 'Coding', 'Math']
        ).fillna(0)
        
        plt.figure(figsize=(12, 8))
        sns.heatmap(specialization_df, annot=True, fmt='.1f', cmap='YlOrRd', cbar_kws={'label': 'Performance Index'})
        plt.title('Creator Performance Specialization Heatmap')
        plt.ylabel('Creator')
        plt.xlabel('Performance Metric')
        plt.tight_layout()
        plt.savefig('hypothesis_2_creator_specialization.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def hypothesis_3_free_vs_paid_performance(self):
        """
        Hypothesis 3: Paid AI models significantly outperform free models
        H0: No difference in performance between free and paid models
        H1: Paid models have significantly higher performance than free models
        """
        print("="*80)
        print("HYPOTHESIS 3: Free vs Paid Model Performance")
        print("="*80)
        
        # Separate free and paid models
        free_models = self.df[self.df['price_1m_blended_3_to_1'] == 0]['ai_intelligence_index'].dropna()
        paid_models = self.df[self.df['price_1m_blended_3_to_1'] > 0]['ai_intelligence_index'].dropna()
        
        if len(free_models) < 5 or len(paid_models) < 5:
            print("Insufficient data for free vs paid comparison")
            return
        
        print(f"Free models: {len(free_models)} samples")
        print(f"Paid models: {len(paid_models)} samples")
        print(f"Free models mean performance: {free_models.mean():.2f}")
        print(f"Paid models mean performance: {paid_models.mean():.2f}")
        
        # Perform independent t-test
        t_stat, p_value = stats.ttest_ind(paid_models, free_models, alternative='greater')
        
        print(f"\nIndependent t-test results:")
        print(f"T-statistic: {t_stat:.4f}")
        print(f"P-value (one-tailed): {p_value:.4f}")
        
        if p_value < 0.05:
            print("✅ REJECT H0: Paid models significantly outperform free models")
        else:
            print("❌ FAIL TO REJECT H0: No significant performance difference")
        
        # Effect size (Cohen's d)
        pooled_std = np.sqrt(((len(paid_models) - 1) * paid_models.var() + 
                             (len(free_models) - 1) * free_models.var()) / 
                            (len(paid_models) + len(free_models) - 2))
        cohens_d = (paid_models.mean() - free_models.mean()) / pooled_std
        
        if abs(cohens_d) >= 0.8:
            effect_desc = "Large"
        elif abs(cohens_d) >= 0.5:
            effect_desc = "Medium"
        elif abs(cohens_d) >= 0.2:
            effect_desc = "Small"
        else:
            effect_desc = "Negligible"
        
        print(f"Cohen's d: {cohens_d:.4f} ({effect_desc} effect)")
        
        self.results['hypothesis_3'] = {
            't_statistic': t_stat,
            'p_value': p_value,
            'significant': p_value < 0.05,
            'cohens_d': cohens_d,
            'effect_size': effect_desc,
            'free_mean': free_models.mean(),
            'paid_mean': paid_models.mean(),
            'free_n': len(free_models),
            'paid_n': len(paid_models)
        }
        
        # Visualization
        plt.figure(figsize=(10, 6))
        plt.boxplot([free_models, paid_models], labels=['Free Models', 'Paid Models'])
        plt.ylabel('AI Intelligence Index')
        plt.title(f'Performance Comparison: Free vs Paid Models\n(t = {t_stat:.4f}, p = {p_value:.4f}, d = {cohens_d:.4f})')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('hypothesis_3_free_vs_paid.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def hypothesis_4_coding_math_correlation(self):
        """
        Hypothesis 4: AI models with strong coding abilities also excel in mathematical reasoning
        H0: No correlation between coding and math performance
        H1: Positive correlation exists between coding and math performance
        """
        print("="*80)
        print("HYPOTHESIS 4: Coding-Math Performance Correlation")
        print("="*80)
        
        # Get complete cases for both metrics
        coding_math_data = self.df[['ai_coding_index', 'ai_math_index']].dropna()
        
        if len(coding_math_data) < 10:
            print("Insufficient data for coding-math correlation analysis")
            return
        
        # Calculate correlation
        correlation, p_value = stats.pearsonr(coding_math_data['ai_coding_index'], 
                                            coding_math_data['ai_math_index'])
        
        print(f"Sample size: {len(coding_math_data)} models")
        print(f"Pearson correlation coefficient: {correlation:.4f}")
        print(f"P-value: {p_value:.4f}")
        
        if p_value < 0.05:
            print("✅ REJECT H0: Significant correlation exists between coding and math performance")
            if correlation > 0:
                print("📈 Positive correlation: Strong coding abilities are associated with strong math abilities")
            else:
                print("📉 Negative correlation: Strong coding abilities are associated with weaker math abilities")
        else:
            print("❌ FAIL TO REJECT H0: No significant correlation between coding and math performance")
        
        # Effect size
        effect_size = abs(correlation)
        if effect_size >= 0.5:
            effect_desc = "Large"
        elif effect_size >= 0.3:
            effect_desc = "Medium"
        elif effect_size >= 0.1:
            effect_desc = "Small"
        else:
            effect_desc = "Negligible"
        
        print(f"Effect size: {effect_desc} (r = {correlation:.4f})")
        
        self.results['hypothesis_4'] = {
            'correlation': correlation,
            'p_value': p_value,
            'significant': p_value < 0.05,
            'effect_size': effect_desc,
            'sample_size': len(coding_math_data)
        }
        
        # Visualization
        plt.figure(figsize=(10, 6))
        plt.scatter(coding_math_data['ai_coding_index'], coding_math_data['ai_math_index'], alpha=0.6)
        plt.xlabel('AI Coding Index')
        plt.ylabel('AI Math Index')
        plt.title(f'Coding vs Math Performance\n(r = {correlation:.4f}, p = {p_value:.4f})')
        
        # Add trend line
        z = np.polyfit(coding_math_data['ai_coding_index'], coding_math_data['ai_math_index'], 1)
        p = np.poly1d(z)
        plt.plot(coding_math_data['ai_coding_index'], p(coding_math_data['ai_coding_index']), "r--", alpha=0.8)
        
        plt.tight_layout()
        plt.savefig('hypothesis_4_coding_math_correlation.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_summary_report(self):
        """Generate a comprehensive summary of all hypothesis testing results."""
        print("="*80)
        print("HYPOTHESIS TESTING SUMMARY REPORT")
        print("="*80)
        
        for i, (hypothesis, results) in enumerate(self.results.items(), 1):
            print(f"\nHypothesis {i}: {hypothesis.replace('_', ' ').title()}")
            print("-" * 50)
            
            if 'significant' in results:
                status = "SIGNIFICANT" if results['significant'] else "NOT SIGNIFICANT"
                print(f"Result: {status}")
                
                if 'p_value' in results:
                    print(f"P-value: {results['p_value']:.4f}")
                
                if 'effect_size' in results:
                    print(f"Effect size: {results['effect_size']}")
                
                if 'sample_size' in results:
                    print(f"Sample size: {results['sample_size']}")

def main():
    """Main hypothesis testing function."""
    # Load the data
    try:
        file_path = Path("ai_models_api_data_20250725_053908.xlsx")
        if not file_path.exists():
            file_path = Path("ai_models_data.xlsx")
        
        if file_path.suffix == '.xlsx':
            excel_file = pd.ExcelFile(file_path)
            if 'AI_Models_Complete' in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name='AI_Models_Complete')
            else:
                df = pd.read_excel(file_path, sheet_name=0)
        
        print(f"Dataset loaded: {df.shape}")
        print("Running hypothesis tests...")
        
        # Initialize hypothesis tester
        tester = AIModelsHypothesisTesting(df)
        
        # Run all hypothesis tests
        tester.hypothesis_1_price_performance_correlation()
        tester.hypothesis_2_creator_specialization()
        tester.hypothesis_3_free_vs_paid_performance()
        tester.hypothesis_4_coding_math_correlation()
        
        # Generate summary report
        tester.generate_summary_report()
        
        print("\nAll hypothesis tests completed!")
        
    except Exception as e:
        print(f"Error in hypothesis testing: {e}")

if __name__ == "__main__":
    main()

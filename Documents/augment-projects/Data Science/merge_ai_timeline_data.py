#!/usr/bin/env python3
"""
AI Timeline Data Merger
从nhlocal.github.io/AiTimeline网站提取AI模型数据并合并到现有Excel文件
"""

import pandas as pd
import re
from datetime import datetime

def extract_nhlocal_timeline_data():
    """从网站内容中提取AI模型时间线数据"""
    
    # 从网站获取的数据 - 手动整理的关键AI模型发布信息
    timeline_data = [
        # 2022年
        {'year': 2022, 'month': 2, 'day': 1, 'model_name': 'Midjourney v1', 'creator': 'Midjourney', 'category': 'IMAGE_GENERATION'},
        {'year': 2022, 'month': 3, 'day': 1, 'model_name': 'text-davinci-002', 'creator': 'OpenAI', 'category': 'LANGUAGE_MODEL'},
        {'year': 2022, 'month': 3, 'day': 1, 'model_name': 'code-davinci-002', 'creator': 'OpenAI', 'category': 'CODE_MODEL'},
        {'year': 2022, 'month': 4, 'day': 1, 'model_name': 'Midjourney v2', 'creator': 'Midjourney', 'category': 'IMAGE_GENERATION'},
        {'year': 2022, 'month': 4, 'day': 6, 'model_name': 'DALL-E 2', 'creator': 'OpenAI', 'category': 'IMAGE_GENERATION'},
        {'year': 2022, 'month': 7, 'day': 1, 'model_name': 'Midjourney v3', 'creator': 'Midjourney', 'category': 'IMAGE_GENERATION'},
        {'year': 2022, 'month': 8, 'day': 22, 'model_name': 'Stable Diffusion 1.4', 'creator': 'Stability AI', 'category': 'IMAGE_GENERATION'},
        {'year': 2022, 'month': 10, 'day': 1, 'model_name': 'Stable Diffusion 1.5', 'creator': 'Stability AI', 'category': 'IMAGE_GENERATION'},
        {'year': 2022, 'month': 11, 'day': 30, 'model_name': 'ChatGPT', 'creator': 'OpenAI', 'category': 'LANGUAGE_MODEL'},
        {'year': 2022, 'month': 11, 'day': 1, 'model_name': 'Midjourney v4', 'creator': 'Midjourney', 'category': 'IMAGE_GENERATION'},
        {'year': 2022, 'month': 11, 'day': 1, 'model_name': 'Stable Diffusion 2.0', 'creator': 'Stability AI', 'category': 'IMAGE_GENERATION'},
        {'year': 2022, 'month': 12, 'day': 1, 'model_name': 'Stable Diffusion 2.1', 'creator': 'Stability AI', 'category': 'IMAGE_GENERATION'},
        
        # 2023年
        {'year': 2023, 'month': 2, 'day': 24, 'model_name': 'LLaMA', 'creator': 'Meta', 'category': 'LANGUAGE_MODEL'},
        {'year': 2023, 'month': 2, 'day': 1, 'model_name': 'Bing AI', 'creator': 'Microsoft', 'category': 'LANGUAGE_MODEL'},
        {'year': 2023, 'month': 3, 'day': 1, 'model_name': 'Midjourney v5', 'creator': 'Midjourney', 'category': 'IMAGE_GENERATION'},
        {'year': 2023, 'month': 3, 'day': 14, 'model_name': 'GPT-4', 'creator': 'OpenAI', 'category': 'LANGUAGE_MODEL'},
        {'year': 2023, 'month': 3, 'day': 21, 'model_name': 'Bard', 'creator': 'Google', 'category': 'LANGUAGE_MODEL'},
        {'year': 2023, 'month': 4, 'day': 1, 'model_name': 'Firefly', 'creator': 'Adobe', 'category': 'IMAGE_GENERATION'},
        {'year': 2023, 'month': 5, 'day': 1, 'model_name': 'Midjourney v5.1', 'creator': 'Midjourney', 'category': 'IMAGE_GENERATION'},
        {'year': 2023, 'month': 5, 'day': 10, 'model_name': 'PaLM 2', 'creator': 'Google', 'category': 'LANGUAGE_MODEL'},
        {'year': 2023, 'month': 6, 'day': 1, 'model_name': 'Midjourney v5.2', 'creator': 'Midjourney', 'category': 'IMAGE_GENERATION'},
        {'year': 2023, 'month': 7, 'day': 1, 'model_name': 'Stable Diffusion XL 1.0', 'creator': 'Stability AI', 'category': 'IMAGE_GENERATION'},
        {'year': 2023, 'month': 7, 'day': 11, 'model_name': 'Claude 2', 'creator': 'Anthropic', 'category': 'LANGUAGE_MODEL'},
        {'year': 2023, 'month': 7, 'day': 18, 'model_name': 'LLaMA 2', 'creator': 'Meta', 'category': 'LANGUAGE_MODEL'},
        {'year': 2023, 'month': 10, 'day': 1, 'model_name': 'DALL-E 3', 'creator': 'OpenAI', 'category': 'IMAGE_GENERATION'},
        {'year': 2023, 'month': 10, 'day': 1, 'model_name': 'Firefly 2', 'creator': 'Adobe', 'category': 'IMAGE_GENERATION'},
        {'year': 2023, 'month': 11, 'day': 1, 'model_name': 'Stable Diffusion XL Turbo', 'creator': 'Stability AI', 'category': 'IMAGE_GENERATION'},
        {'year': 2023, 'month': 12, 'day': 1, 'model_name': 'Midjourney v6', 'creator': 'Midjourney', 'category': 'IMAGE_GENERATION'},
        {'year': 2023, 'month': 12, 'day': 6, 'model_name': 'Gemini Pro', 'creator': 'Google', 'category': 'LANGUAGE_MODEL'},
        {'year': 2023, 'month': 12, 'day': 1, 'model_name': 'Grok AI', 'creator': 'xAI', 'category': 'LANGUAGE_MODEL'},
        
        # 2024年
        {'year': 2024, 'month': 2, 'day': 1, 'model_name': 'Stable Diffusion 3', 'creator': 'Stability AI', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 2, 'day': 8, 'model_name': 'Gemini Pro 1.5', 'creator': 'Google', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 2, 'day': 15, 'model_name': 'Sora', 'creator': 'OpenAI', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 3, 'day': 1, 'model_name': 'Grok 1.5', 'creator': 'xAI', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 3, 'day': 4, 'model_name': 'Claude 3', 'creator': 'Anthropic', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 3, 'day': 1, 'model_name': 'Suno v3', 'creator': 'Suno AI', 'category': 'MUSIC_GENERATION'},
        {'year': 2024, 'month': 4, 'day': 1, 'model_name': 'Stable Audio 2.0', 'creator': 'Stability AI', 'category': 'MUSIC_GENERATION'},
        {'year': 2024, 'month': 4, 'day': 1, 'model_name': 'Grok-1.5V', 'creator': 'xAI', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 4, 'day': 1, 'model_name': 'Mixtral 8x22B', 'creator': 'Mistral', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 4, 'day': 18, 'model_name': 'LLaMA 3', 'creator': 'Meta', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 4, 'day': 1, 'model_name': 'Phi-3-mini', 'creator': 'Microsoft', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 4, 'day': 1, 'model_name': 'Firefly 3', 'creator': 'Adobe', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 4, 'day': 1, 'model_name': 'Reka AI', 'creator': 'Reka AI', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 4, 'day': 1, 'model_name': 'OpenELM', 'creator': 'Apple', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 5, 'day': 13, 'model_name': 'GPT-4o', 'creator': 'OpenAI', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Gemini Flash 1.5', 'creator': 'Google', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Imagen 3', 'creator': 'Google', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Music AI', 'creator': 'Google', 'category': 'MUSIC_GENERATION'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Veo', 'creator': 'Google', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Astra', 'creator': 'Google', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Phi-3 Small', 'creator': 'Microsoft', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Phi-3 Medium', 'creator': 'Microsoft', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Phi-3 Vision', 'creator': 'Microsoft', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Chameleon', 'creator': 'Meta', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Mistral-7B-Instruct-v0.3', 'creator': 'Mistral', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Suno v3.5', 'creator': 'Suno AI', 'category': 'MUSIC_GENERATION'},
        {'year': 2024, 'month': 5, 'day': 1, 'model_name': 'Codestral', 'creator': 'Mistral', 'category': 'CODE_MODEL'},
        
        # 2024年6月-12月
        {'year': 2024, 'month': 6, 'day': 1, 'model_name': 'Stable Diffusion 3 Medium', 'creator': 'Stability AI', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 6, 'day': 1, 'model_name': 'Apple Intelligence', 'creator': 'Apple', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 6, 'day': 1, 'model_name': 'DeepSeekCoderV2', 'creator': 'DeepSeek', 'category': 'CODE_MODEL'},
        {'year': 2024, 'month': 6, 'day': 1, 'model_name': 'Gen3 Alpha', 'creator': 'Runway', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 6, 'day': 20, 'model_name': 'Claude Sonnet 3.5', 'creator': 'Anthropic', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 6, 'day': 1, 'model_name': 'Florence 2', 'creator': 'Microsoft', 'category': 'VISION_MODEL'},
        {'year': 2024, 'month': 6, 'day': 1, 'model_name': 'Gemma 2', 'creator': 'Google', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 7, 'day': 18, 'model_name': 'GPT-4o mini', 'creator': 'OpenAI', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 7, 'day': 23, 'model_name': 'Llama 3.1', 'creator': 'Meta', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'Codestral Mamba', 'creator': 'Mistral', 'category': 'CODE_MODEL'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'Mistral NeMo', 'creator': 'Mistral', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'Mathstral', 'creator': 'Mistral', 'category': 'MATH_MODEL'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'AlphaProof', 'creator': 'Google DeepMind', 'category': 'MATH_MODEL'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'AlphaGeometry 2', 'creator': 'Google DeepMind', 'category': 'MATH_MODEL'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'SearchGPT', 'creator': 'OpenAI', 'category': 'SEARCH_MODEL'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'Udio v1.5', 'creator': 'Udio', 'category': 'MUSIC_GENERATION'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'Mistral Large 2', 'creator': 'Mistral', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'Midjourney v6.1', 'creator': 'Midjourney', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 7, 'day': 1, 'model_name': 'Gemma 2 2B', 'creator': 'Google', 'category': 'LANGUAGE_MODEL'},
        
        # 2024年8月-12月
        {'year': 2024, 'month': 8, 'day': 1, 'model_name': 'Flux', 'creator': 'Black Forest Labs', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 8, 'day': 6, 'model_name': 'GPT-4o 0806', 'creator': 'OpenAI', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 8, 'day': 1, 'model_name': 'Grok 2', 'creator': 'xAI', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 8, 'day': 1, 'model_name': 'Grok 2 mini', 'creator': 'xAI', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 8, 'day': 1, 'model_name': 'Phi 3.5', 'creator': 'Microsoft', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 8, 'day': 1, 'model_name': 'Ideogram 2.0', 'creator': 'Ideogram', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 8, 'day': 1, 'model_name': 'Dream Machine 1.5', 'creator': 'Luma', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 9, 'day': 1, 'model_name': 'Pixtral12B', 'creator': 'Mistral', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 9, 'day': 12, 'model_name': 'o1 preview', 'creator': 'OpenAI', 'category': 'REASONING_MODEL'},
        {'year': 2024, 'month': 9, 'day': 12, 'model_name': 'o1 mini', 'creator': 'OpenAI', 'category': 'REASONING_MODEL'},
        {'year': 2024, 'month': 9, 'day': 1, 'model_name': 'Qwen 2.5', 'creator': 'Alibaba', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 9, 'day': 1, 'model_name': 'KLING 1.5', 'creator': 'Kuaishou', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 9, 'day': 25, 'model_name': 'Llama 3.2', 'creator': 'Meta', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 9, 'day': 1, 'model_name': 'Moshi', 'creator': 'Kyutai', 'category': 'VOICE_MODEL'},
        {'year': 2024, 'month': 9, 'day': 1, 'model_name': 'Mistral Small', 'creator': 'Mistral', 'category': 'LANGUAGE_MODEL'},
        
        # 2024年10月-12月
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Flux 1.1 Pro', 'creator': 'Black Forest Labs', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Movie Gen', 'creator': 'Meta', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Video Model 1.5', 'creator': 'Pika', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Firefly Video', 'creator': 'Adobe', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Aria', 'creator': 'Rhymes AI', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Meta Spirit LM', 'creator': 'Meta', 'category': 'VOICE_MODEL'},
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Ministral', 'creator': 'Mistral', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Janus AI', 'creator': 'DeepSeek', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Stable Diffusion 3.5', 'creator': 'Stability AI', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 10, 'day': 22, 'model_name': 'Claude 3.5 Sonnet New', 'creator': 'Anthropic', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 10, 'day': 22, 'model_name': 'Claude 3.5 Haiku', 'creator': 'Anthropic', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 10, 'day': 1, 'model_name': 'Recraft v3', 'creator': 'Recraft', 'category': 'IMAGE_GENERATION'},
        
        # 2024年11月-12月
        {'year': 2024, 'month': 11, 'day': 1, 'model_name': 'QwQ 32B Preview', 'creator': 'Alibaba', 'category': 'REASONING_MODEL'},
        {'year': 2024, 'month': 11, 'day': 1, 'model_name': 'Qwen2.5 Coder 32B', 'creator': 'Alibaba', 'category': 'CODE_MODEL'},
        {'year': 2024, 'month': 11, 'day': 1, 'model_name': 'DeepSeek-R1-Lite-Preview', 'creator': 'DeepSeek', 'category': 'REASONING_MODEL'},
        {'year': 2024, 'month': 11, 'day': 1, 'model_name': 'Suno v4', 'creator': 'Suno AI', 'category': 'MUSIC_GENERATION'},
        {'year': 2024, 'month': 11, 'day': 1, 'model_name': 'Pixtral Large', 'creator': 'Mistral', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 12, 'day': 1, 'model_name': 'NOVA', 'creator': 'Amazon', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 12, 'day': 9, 'model_name': 'SORA', 'creator': 'OpenAI', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 12, 'day': 5, 'model_name': 'O1', 'creator': 'OpenAI', 'category': 'REASONING_MODEL'},
        {'year': 2024, 'month': 12, 'day': 5, 'model_name': 'O1 Pro', 'creator': 'OpenAI', 'category': 'REASONING_MODEL'},
        {'year': 2024, 'month': 12, 'day': 11, 'model_name': 'Gemini 2.0 Flash', 'creator': 'Google', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 12, 'day': 1, 'model_name': 'Veo 2', 'creator': 'Google', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 12, 'day': 1, 'model_name': 'Aurora', 'creator': 'xAI', 'category': 'IMAGE_GENERATION'},
        {'year': 2024, 'month': 12, 'day': 11, 'model_name': 'Phi4', 'creator': 'Microsoft', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 12, 'day': 6, 'model_name': 'Llama 3.3 70B', 'creator': 'Meta', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 12, 'day': 1, 'model_name': 'PaliGemma 2', 'creator': 'Google', 'category': 'MULTIMODAL'},
        {'year': 2024, 'month': 12, 'day': 1, 'model_name': 'Pika 2.0', 'creator': 'Pika Labs', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 12, 'day': 1, 'model_name': 'Apollo', 'creator': 'Meta', 'category': 'VIDEO_GENERATION'},
        {'year': 2024, 'month': 12, 'day': 26, 'model_name': 'Deepseek V3', 'creator': 'DeepSeek', 'category': 'LANGUAGE_MODEL'},
        {'year': 2024, 'month': 12, 'day': 1, 'model_name': 'QVQ-72B-Preview', 'creator': 'Alibaba', 'category': 'REASONING_MODEL'},
        {'year': 2024, 'month': 12, 'day': 20, 'model_name': 'O3', 'creator': 'OpenAI', 'category': 'REASONING_MODEL'},
        {'year': 2024, 'month': 12, 'day': 20, 'model_name': 'O3 Mini', 'creator': 'OpenAI', 'category': 'REASONING_MODEL'},
        {'year': 2024, 'month': 12, 'day': 1, 'model_name': 'Kling 1.6', 'creator': 'Kuaishou', 'category': 'VIDEO_GENERATION'},
        
        # 2025年预测
        {'year': 2025, 'month': 1, 'day': 1, 'model_name': 'Operator', 'creator': 'OpenAI', 'category': 'AGENT_MODEL'},
        {'year': 2025, 'month': 1, 'day': 1, 'model_name': 'Gemini Flash Thinking 0121', 'creator': 'Google', 'category': 'REASONING_MODEL'},
        {'year': 2025, 'month': 1, 'day': 1, 'model_name': 'DeepSeek R1', 'creator': 'DeepSeek', 'category': 'REASONING_MODEL'},
        {'year': 2025, 'month': 1, 'day': 1, 'model_name': 'DeepSeek R1-Zero', 'creator': 'DeepSeek', 'category': 'REASONING_MODEL'},
        {'year': 2025, 'month': 1, 'day': 1, 'model_name': 'Janus Pro 7B', 'creator': 'DeepSeek', 'category': 'MULTIMODAL'},
        {'year': 2025, 'month': 1, 'day': 1, 'model_name': 'Qwen2.5-Max', 'creator': 'Alibaba', 'category': 'LANGUAGE_MODEL'},
        {'year': 2025, 'month': 1, 'day': 1, 'model_name': 'o3 mini', 'creator': 'OpenAI', 'category': 'REASONING_MODEL'},
    ]
    
    return timeline_data

def merge_with_existing_excel(new_data, existing_file):
    """将新数据与现有Excel文件合并"""
    
    # 读取现有Excel文件
    existing_df = pd.read_excel(existing_file, sheet_name='AI_Timeline')
    
    # 转换新数据为DataFrame
    new_df = pd.DataFrame(new_data)
    
    # 添加缺失的列
    new_df['date'] = pd.to_datetime(new_df[['year', 'month', 'day']])
    new_df['headline'] = new_df['model_name']
    new_df['description'] = new_df.apply(lambda row: f"{row['creator']} releases {row['model_name']}, a {row['category'].replace('_', ' ').lower()} model.", axis=1)
    new_df['importance'] = 2.0  # 默认重要性
    new_df['category'] = new_df['category'].str.replace('_', ' ')
    
    # 重新排列列顺序以匹配现有格式
    new_df = new_df[['date', 'year', 'month', 'day', 'headline', 'description', 'importance', 'category']]
    
    # 合并数据
    combined_df = pd.concat([existing_df, new_df], ignore_index=True)
    
    # 去重 (基于headline和date)
    combined_df = combined_df.drop_duplicates(subset=['headline', 'date'], keep='first')
    
    # 按日期排序
    combined_df = combined_df.sort_values('date').reset_index(drop=True)
    
    return combined_df

def create_enhanced_excel(combined_df, filename):
    """创建增强版Excel文件"""
    
    # 创建新的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_filename = f'AI_Timeline_Enhanced_{timestamp}.xlsx'
    
    with pd.ExcelWriter(new_filename, engine='openpyxl') as writer:
        # 主要时间线数据
        combined_df.to_excel(writer, sheet_name='AI_Timeline_Complete', index=False)
        
        # 按年份分组
        year_summary = combined_df.groupby('year').agg({
            'headline': 'count',
            'importance': 'mean',
            'category': lambda x: ', '.join(x.value_counts().head(3).index)
        }).rename(columns={
            'headline': 'event_count',
            'importance': 'avg_importance',
            'category': 'top_categories'
        })
        year_summary.to_excel(writer, sheet_name='Year_Summary')
        
        # 按类别分组
        category_summary = combined_df.groupby('category').agg({
            'headline': 'count',
            'importance': 'mean',
            'year': lambda x: f"{x.min()}-{x.max()}"
        }).rename(columns={
            'headline': 'event_count',
            'importance': 'avg_importance',
            'year': 'year_range'
        })
        category_summary.to_excel(writer, sheet_name='Category_Summary')
        
        # 模型发布时间线 (只包含模型发布)
        model_releases = combined_df[combined_df['category'].str.contains('MODEL|GENERATION', case=False, na=False)]
        model_releases.to_excel(writer, sheet_name='Model_Releases', index=False)
        
        # 2024年重要事件
        events_2024 = combined_df[combined_df['year'] == 2024]
        events_2024.to_excel(writer, sheet_name='Events_2024', index=False)
        
        # 2025年预测
        events_2025 = combined_df[combined_df['year'] == 2025]
        events_2025.to_excel(writer, sheet_name='Predictions_2025', index=False)
    
    return new_filename

if __name__ == "__main__":
    try:
        print("🚀 开始合并AI时间线数据...")
        
        # 提取新数据
        new_data = extract_nhlocal_timeline_data()
        print(f"✅ 从nhlocal网站提取了 {len(new_data)} 个新的AI模型事件")
        
        # 合并数据
        existing_file = 'AI_Timeline_Complete_20250726_194054.xlsx'
        combined_df = merge_with_existing_excel(new_data, existing_file)
        print(f"✅ 合并后总计 {len(combined_df)} 个事件")
        
        # 创建增强版Excel文件
        new_filename = create_enhanced_excel(combined_df, existing_file)
        
        print(f"\\n🎉 任务完成！")
        print(f"📁 新文件: {new_filename}")
        print(f"📊 总事件数: {len(combined_df)}")
        print(f"📅 时间跨度: {combined_df['year'].min()} - {combined_df['year'].max()}")
        
        # 显示统计信息
        print("\\n📈 按年份分布:")
        year_counts = combined_df['year'].value_counts().sort_index()
        for year, count in year_counts.items():
            print(f"  {int(year)}: {count} 个事件")
        
        print("\\n🏷️ 按类别分布:")
        category_counts = combined_df['category'].value_counts().head(10)
        for category, count in category_counts.items():
            print(f"  {category}: {count} 个事件")
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()

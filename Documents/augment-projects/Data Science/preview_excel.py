#!/usr/bin/env python3
"""
Script to preview the Excel file contents.
"""

import pandas as pd
from pathlib import Path

def preview_excel_file(file_path):
    """Preview the Excel file contents."""
    try:
        # Read all sheets
        excel_file = pd.ExcelFile(file_path)
        print(f"Excel file: {file_path}")
        print(f"Sheet names: {excel_file.sheet_names}")
        print("=" * 60)
        
        # Preview each sheet
        for sheet_name in excel_file.sheet_names:
            print(f"\n📊 SHEET: {sheet_name}")
            print("-" * 40)
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"Shape: {df.shape} (rows, columns)")
            print(f"Columns: {list(df.columns)}")
            
            # Show first few rows
            print("\nFirst 3 rows:")
            print(df.head(3).to_string(max_cols=6, max_colwidth=20))
            
            # Show some statistics for numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                print(f"\nNumeric columns summary:")
                for col in numeric_cols[:5]:  # Show first 5 numeric columns
                    non_null_count = df[col].count()
                    if non_null_count > 0:
                        print(f"  {col}: {non_null_count} non-null values, "
                              f"range: {df[col].min():.3f} - {df[col].max():.3f}")
            
            print("\n" + "=" * 60)
            
    except Exception as e:
        print(f"Error reading Excel file: {e}")

def main():
    """Main function."""
    excel_file = Path("ai_models_data.xlsx")
    
    if not excel_file.exists():
        print(f"Excel file not found: {excel_file}")
        return
    
    preview_excel_file(excel_file)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Script to convert AI model data from JSON to Excel format.
"""

import json
import pandas as pd
from pathlib import Path
import sys

def load_json_data(file_path):
    """Load JSON data from file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()

        # Try to find the end of the JSON object
        # Look for the last closing brace that would complete the JSON
        brace_count = 0
        last_valid_pos = 0

        for i, char in enumerate(content):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    last_valid_pos = i + 1
                    break

        if last_valid_pos > 0:
            content = content[:last_valid_pos]

        data = json.loads(content)
        return data
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        print(f"Trying alternative parsing method...")

        # Alternative: try to extract just the data array
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # Find the start of the data array
            data_start = content.find('"data":[')
            if data_start != -1:
                # Find the matching closing bracket for the data array
                bracket_count = 0
                start_pos = content.find('[', data_start)

                for i in range(start_pos, len(content)):
                    if content[i] == '[':
                        bracket_count += 1
                    elif content[i] == ']':
                        bracket_count -= 1
                        if bracket_count == 0:
                            data_array_str = content[start_pos:i+1]
                            data_array = json.loads(data_array_str)
                            return {"data": data_array}

            return None
        except Exception as e2:
            print(f"Alternative parsing also failed: {e2}")
            return None

def flatten_nested_data(data_list):
    """Flatten nested JSON structure for Excel export."""
    flattened_data = []
    
    for item in data_list:
        flat_item = {}
        
        # Basic fields
        flat_item['id'] = item.get('id', '')
        flat_item['name'] = item.get('name', '')
        flat_item['slug'] = item.get('slug', '')
        flat_item['release_date'] = item.get('release_date', '')
        
        # Model creator fields
        creator = item.get('model_creator', {})
        flat_item['creator_id'] = creator.get('id', '')
        flat_item['creator_name'] = creator.get('name', '')
        flat_item['creator_slug'] = creator.get('slug', '')
        
        # Evaluation fields
        evaluations = item.get('evaluations', {})
        flat_item['ai_intelligence_index'] = evaluations.get('artificial_analysis_intelligence_index', '')
        flat_item['ai_coding_index'] = evaluations.get('artificial_analysis_coding_index', '')
        flat_item['ai_math_index'] = evaluations.get('artificial_analysis_math_index', '')
        flat_item['mmlu_pro'] = evaluations.get('mmlu_pro', '')
        flat_item['gpqa'] = evaluations.get('gpqa', '')
        flat_item['hle'] = evaluations.get('hle', '')
        flat_item['livecodebench'] = evaluations.get('livecodebench', '')
        flat_item['scicode'] = evaluations.get('scicode', '')
        flat_item['math_500'] = evaluations.get('math_500', '')
        flat_item['aime'] = evaluations.get('aime', '')
        
        # Pricing fields
        pricing = item.get('pricing', {})
        flat_item['price_1m_blended_3_to_1'] = pricing.get('price_1m_blended_3_to_1', '')
        flat_item['price_1m_input_tokens'] = pricing.get('price_1m_input_tokens', '')
        flat_item['price_1m_output_tokens'] = pricing.get('price_1m_output_tokens', '')
        
        # Performance fields
        flat_item['median_output_tokens_per_second'] = item.get('median_output_tokens_per_second', '')
        flat_item['median_time_to_first_token_seconds'] = item.get('median_time_to_first_token_seconds', '')
        flat_item['median_time_to_first_answer_token'] = item.get('median_time_to_first_answer_token', '')
        
        flattened_data.append(flat_item)
    
    return flattened_data

def create_excel_file(data, output_path):
    """Create Excel file with multiple sheets."""
    try:
        # Create main dataframe
        df_main = pd.DataFrame(data)
        
        # Create Excel writer object
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # Main sheet with all data
            df_main.to_excel(writer, sheet_name='AI_Models_Complete', index=False)
            
            # Summary sheet with key metrics
            summary_cols = ['name', 'creator_name', 'release_date', 'ai_intelligence_index', 
                          'ai_coding_index', 'ai_math_index', 'price_1m_blended_3_to_1']
            df_summary = df_main[summary_cols].copy()
            df_summary.to_excel(writer, sheet_name='Summary', index=False)
            
            # Pricing sheet
            pricing_cols = ['name', 'creator_name', 'price_1m_blended_3_to_1', 
                          'price_1m_input_tokens', 'price_1m_output_tokens']
            df_pricing = df_main[pricing_cols].copy()
            df_pricing.to_excel(writer, sheet_name='Pricing', index=False)
            
            # Performance sheet
            performance_cols = ['name', 'creator_name', 'median_output_tokens_per_second',
                              'median_time_to_first_token_seconds', 'median_time_to_first_answer_token']
            df_performance = df_main[performance_cols].copy()
            df_performance.to_excel(writer, sheet_name='Performance', index=False)
            
            # Evaluations sheet
            eval_cols = ['name', 'creator_name', 'ai_intelligence_index', 'ai_coding_index', 
                        'ai_math_index', 'mmlu_pro', 'gpqa', 'hle', 'livecodebench', 
                        'scicode', 'math_500', 'aime']
            df_evaluations = df_main[eval_cols].copy()
            df_evaluations.to_excel(writer, sheet_name='Evaluations', index=False)
        
        print(f"Excel file created successfully: {output_path}")
        print(f"Total records: {len(df_main)}")
        print(f"Sheets created: AI_Models_Complete, Summary, Pricing, Performance, Evaluations")
        
    except Exception as e:
        print(f"Error creating Excel file: {e}")

def main():
    """Main function to convert JSON to Excel."""
    # File paths
    json_file = Path("data.js")
    excel_file = Path("ai_models_data.xlsx")
    
    # Check if input file exists
    if not json_file.exists():
        print(f"Error: {json_file} not found!")
        return
    
    # Load JSON data
    print("Loading JSON data...")
    json_data = load_json_data(json_file)
    
    if json_data is None:
        return
    
    # Extract the data array
    if 'data' in json_data:
        models_data = json_data['data']
        print(f"Found {len(models_data)} AI models in the dataset")
    else:
        print("Error: 'data' key not found in JSON")
        return
    
    # Flatten the data
    print("Flattening nested data structure...")
    flattened_data = flatten_nested_data(models_data)
    
    # Create Excel file
    print("Creating Excel file...")
    create_excel_file(flattened_data, excel_file)

if __name__ == "__main__":
    main()

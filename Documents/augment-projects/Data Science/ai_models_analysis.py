#!/usr/bin/env python3
"""
AI Models Dataset Analysis Project
Data Science Project - Comprehensive Analysis of AI Model Performance and Pricing

Team Members:
- Data Analyst 1: Data preprocessing and exploration
- Data Analyst 2: Visualization and statistical analysis  
- Data Analyst 3: Hypothesis testing and insights

Project Goals:
1. Analyze AI model performance across different metrics
2. Investigate pricing patterns and value propositions
3. Identify relationships between model characteristics and performance
4. Test hypotheses about model creators and their specializations
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class AIModelsAnalysis:
    def __init__(self, file_path):
        """Initialize the analysis with the dataset."""
        self.file_path = file_path
        self.df = None
        self.load_data()
        
    def load_data(self):
        """Load and initial preprocessing of the dataset."""
        try:
            # Try to load the Excel file
            if self.file_path.suffix == '.xlsx':
                # Check if it has multiple sheets
                excel_file = pd.ExcelFile(self.file_path)
                print(f"Available sheets: {excel_file.sheet_names}")
                
                # Load the main sheet (assuming it's the first one or 'AI_Models_Complete')
                if 'AI_Models_Complete' in excel_file.sheet_names:
                    self.df = pd.read_excel(self.file_path, sheet_name='AI_Models_Complete')
                else:
                    self.df = pd.read_excel(self.file_path, sheet_name=0)
            else:
                self.df = pd.read_csv(self.file_path)
                
            print(f"Dataset loaded successfully!")
            print(f"Shape: {self.df.shape}")
            print(f"Columns: {list(self.df.columns)}")
            
        except Exception as e:
            print(f"Error loading data: {e}")
            # Fallback to the converted data
            try:
                self.df = pd.read_excel("ai_models_data.xlsx", sheet_name='AI_Models_Complete')
                print("Loaded fallback dataset successfully!")
            except:
                print("Could not load any dataset!")
                
    def basic_info(self):
        """Display basic information about the dataset."""
        print("="*60)
        print("DATASET OVERVIEW")
        print("="*60)
        
        print(f"Dataset shape: {self.df.shape}")
        print(f"Number of AI models: {len(self.df)}")
        print(f"Number of features: {len(self.df.columns)}")
        
        print("\nColumn Information:")
        print(self.df.info())
        
        print("\nMissing Values:")
        missing = self.df.isnull().sum()
        missing_pct = (missing / len(self.df)) * 100
        missing_df = pd.DataFrame({
            'Missing Count': missing,
            'Missing %': missing_pct
        }).sort_values('Missing %', ascending=False)
        print(missing_df[missing_df['Missing Count'] > 0])
        
        print("\nBasic Statistics:")
        print(self.df.describe())
        
    def preprocess_data(self):
        """Clean and preprocess the data."""
        print("="*60)
        print("DATA PREPROCESSING")
        print("="*60)
        
        # Create a copy for processing
        df_processed = self.df.copy()
        
        # Convert release_date to datetime
        if 'release_date' in df_processed.columns:
            df_processed['release_date'] = pd.to_datetime(df_processed['release_date'])
            df_processed['release_year'] = df_processed['release_date'].dt.year
            df_processed['release_month'] = df_processed['release_date'].dt.month
        
        # Create performance categories
        numeric_cols = df_processed.select_dtypes(include=[np.number]).columns
        
        # Create overall performance score (if intelligence index exists)
        if 'ai_intelligence_index' in df_processed.columns:
            df_processed['performance_category'] = pd.cut(
                df_processed['ai_intelligence_index'], 
                bins=[0, 30, 50, 70, 100], 
                labels=['Low', 'Medium', 'High', 'Excellent'],
                include_lowest=True
            )
        
        # Create pricing categories
        if 'price_1m_blended_3_to_1' in df_processed.columns:
            # Handle zero prices separately
            df_processed['is_free'] = df_processed['price_1m_blended_3_to_1'] == 0
            
            # Create pricing tiers for non-free models
            non_free = df_processed[df_processed['price_1m_blended_3_to_1'] > 0]
            if len(non_free) > 0:
                price_bins = [0, 1, 5, 20, float('inf')]
                price_labels = ['Free', 'Budget', 'Mid-tier', 'Premium']
                df_processed['price_category'] = pd.cut(
                    df_processed['price_1m_blended_3_to_1'],
                    bins=price_bins,
                    labels=price_labels,
                    include_lowest=True
                )
        
        self.df_processed = df_processed
        print("Data preprocessing completed!")
        print(f"Added columns: release_year, release_month, performance_category, price_category")
        
        return df_processed

def main():
    """Main analysis function."""
    # Initialize analysis
    file_path = Path("ai_models_api_data_20250725_053908.xlsx")
    if not file_path.exists():
        file_path = Path("ai_models_data.xlsx")
    
    analyzer = AIModelsAnalysis(file_path)
    
    if analyzer.df is None:
        print("Could not load dataset. Exiting...")
        return
    
    # Basic information
    analyzer.basic_info()
    
    # Preprocess data
    df_processed = analyzer.preprocess_data()
    
    print("\nPreprocessing completed! Ready for detailed analysis...")
    print("Run the visualization and hypothesis testing scripts next.")

if __name__ == "__main__":
    main()

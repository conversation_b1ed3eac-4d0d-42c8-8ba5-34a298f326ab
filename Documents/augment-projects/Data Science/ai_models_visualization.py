#!/usr/bin/env python3
"""
AI Models Dataset Visualization Module
Advanced visualizations for AI model analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class AIModelsVisualizer:
    def __init__(self, df):
        """Initialize with preprocessed dataframe."""
        self.df = df
        
    def create_performance_overview(self):
        """Create comprehensive performance overview visualizations."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('AI Models Performance Overview', fontsize=16, fontweight='bold')
        
        # 1. Intelligence Index Distribution
        axes[0,0].hist(self.df['intelligence_index'].dropna(), bins=30, alpha=0.7, color='skyblue')
        axes[0,0].set_title('AI Intelligence Index Distribution')
        axes[0,0].set_xlabel('Intelligence Index')
        axes[0,0].set_ylabel('Frequency')
        axes[0,0].axvline(self.df['intelligence_index'].mean(), color='red', linestyle='--',
                         label=f'Mean: {self.df["intelligence_index"].mean():.1f}')
        axes[0,0].legend()

        # 2. Performance by Creator (Top 10)
        creator_performance = self.df.groupby('creator_name')['intelligence_index'].agg(['mean', 'count']).reset_index()
        creator_performance = creator_performance[creator_performance['count'] >= 2].sort_values('mean', ascending=False).head(10)

        axes[0,1].barh(creator_performance['creator_name'], creator_performance['mean'])
        axes[0,1].set_title('Average Intelligence Index by Creator (Top 10)')
        axes[0,1].set_xlabel('Average Intelligence Index')

        # 3. Coding vs Math Performance
        coding_math = self.df[['coding_index', 'math_index']].dropna()
        axes[1,0].scatter(coding_math['coding_index'], coding_math['math_index'], alpha=0.6)
        axes[1,0].set_xlabel('Coding Index')
        axes[1,0].set_ylabel('Math Index')
        axes[1,0].set_title('Coding vs Math Performance')

        # Add correlation line
        if len(coding_math) > 1:
            z = np.polyfit(coding_math['coding_index'], coding_math['math_index'], 1)
            p = np.poly1d(z)
            axes[1,0].plot(coding_math['coding_index'], p(coding_math['coding_index']), "r--", alpha=0.8)
            corr = coding_math['coding_index'].corr(coding_math['math_index'])
            axes[1,0].text(0.05, 0.95, f'Correlation: {corr:.3f}', transform=axes[1,0].transAxes,
                          bbox=dict(boxstyle="round", facecolor='wheat', alpha=0.5))
        
        # 4. Performance Categories
        if 'performance_category' in self.df.columns:
            perf_counts = self.df['performance_category'].value_counts()
            axes[1,1].pie(perf_counts.values, labels=perf_counts.index, autopct='%1.1f%%')
            axes[1,1].set_title('Performance Category Distribution')
        
        plt.tight_layout()
        plt.savefig('ai_models_performance_overview.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def create_pricing_analysis(self):
        """Create pricing analysis visualizations."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('AI Models Pricing Analysis', fontsize=16, fontweight='bold')
        
        # 1. Price Distribution (excluding free models)
        paid_models = self.df[self.df['price_1m_blended'] > 0]
        axes[0,0].hist(paid_models['price_1m_blended'], bins=30, alpha=0.7, color='lightgreen')
        axes[0,0].set_title('Price Distribution (Paid Models Only)')
        axes[0,0].set_xlabel('Price per 1M Tokens ($)')
        axes[0,0].set_ylabel('Frequency')
        axes[0,0].set_yscale('log')

        # 2. Price vs Performance
        price_perf = self.df[['price_1m_blended', 'intelligence_index']].dropna()
        price_perf = price_perf[price_perf['price_1m_blended'] > 0]  # Exclude free models

        axes[0,1].scatter(price_perf['price_1m_blended'], price_perf['intelligence_index'], alpha=0.6)
        axes[0,1].set_xlabel('Price per 1M Tokens ($)')
        axes[0,1].set_ylabel('Intelligence Index')
        axes[0,1].set_title('Price vs Performance')
        axes[0,1].set_xscale('log')

        # 3. Free vs Paid Models Performance
        free_models = self.df[self.df['price_1m_blended'] == 0]['intelligence_index'].dropna()
        paid_models_perf = self.df[self.df['price_1m_blended'] > 0]['intelligence_index'].dropna()
        
        axes[1,0].boxplot([free_models, paid_models_perf], labels=['Free', 'Paid'])
        axes[1,0].set_title('Performance: Free vs Paid Models')
        axes[1,0].set_ylabel('Intelligence Index')
        
        # 4. Price Categories
        if 'price_category' in self.df.columns:
            price_counts = self.df['price_category'].value_counts()
            axes[1,1].bar(price_counts.index, price_counts.values)
            axes[1,1].set_title('Models by Price Category')
            axes[1,1].set_ylabel('Number of Models')
            axes[1,1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('ai_models_pricing_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def create_creator_analysis(self):
        """Analyze patterns by model creators."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('AI Models Creator Analysis', fontsize=16, fontweight='bold')
        
        # 1. Number of models by creator
        creator_counts = self.df['creator_name'].value_counts().head(10)
        axes[0,0].barh(creator_counts.index, creator_counts.values)
        axes[0,0].set_title('Number of Models by Creator (Top 10)')
        axes[0,0].set_xlabel('Number of Models')
        
        # 2. Average pricing by creator
        creator_pricing = self.df.groupby('creator_name')['price_1m_blended_3_to_1'].agg(['mean', 'count']).reset_index()
        creator_pricing = creator_pricing[creator_pricing['count'] >= 2].sort_values('mean', ascending=False).head(10)
        
        axes[0,1].barh(creator_pricing['creator_name'], creator_pricing['mean'])
        axes[0,1].set_title('Average Pricing by Creator (Top 10)')
        axes[0,1].set_xlabel('Average Price per 1M Tokens ($)')
        
        # 3. Performance specialization heatmap
        specialization_data = []
        top_creators = self.df['creator_name'].value_counts().head(8).index
        
        for creator in top_creators:
            creator_data = self.df[self.df['creator_name'] == creator]
            specialization_data.append([
                creator_data['ai_intelligence_index'].mean(),
                creator_data['ai_coding_index'].mean(),
                creator_data['ai_math_index'].mean()
            ])
        
        specialization_df = pd.DataFrame(
            specialization_data,
            index=top_creators,
            columns=['Intelligence', 'Coding', 'Math']
        )
        
        sns.heatmap(specialization_df, annot=True, fmt='.1f', cmap='YlOrRd', ax=axes[1,0])
        axes[1,0].set_title('Performance Specialization by Creator')
        
        # 4. Release timeline
        if 'release_year' in self.df.columns:
            yearly_releases = self.df.groupby(['release_year', 'creator_name']).size().unstack(fill_value=0)
            yearly_releases = yearly_releases[yearly_releases.sum().sort_values(ascending=False).head(5).index]
            
            yearly_releases.plot(kind='bar', stacked=True, ax=axes[1,1])
            axes[1,1].set_title('Model Releases by Year (Top 5 Creators)')
            axes[1,1].set_xlabel('Year')
            axes[1,1].set_ylabel('Number of Models')
            axes[1,1].tick_params(axis='x', rotation=45)
            axes[1,1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.tight_layout()
        plt.savefig('ai_models_creator_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def create_correlation_matrix(self):
        """Create correlation matrix for numeric variables."""
        # Select numeric columns related to performance and pricing
        numeric_cols = [
            'ai_intelligence_index', 'ai_coding_index', 'ai_math_index',
            'price_1m_blended_3_to_1', 'price_1m_input_tokens', 'price_1m_output_tokens',
            'median_output_tokens_per_second', 'median_time_to_first_token_seconds'
        ]
        
        # Filter columns that exist in the dataset
        available_cols = [col for col in numeric_cols if col in self.df.columns]
        correlation_data = self.df[available_cols].corr()
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(correlation_data, annot=True, cmap='coolwarm', center=0, 
                   square=True, linewidths=0.5)
        plt.title('Correlation Matrix: AI Model Metrics', fontsize=14, fontweight='bold')
        plt.tight_layout()
        plt.savefig('ai_models_correlation_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """Main visualization function."""
    # Load the preprocessed data
    try:
        # Try to load from the analysis script output or directly from Excel
        file_path = Path("ai_models_api_data_20250725_053908.xlsx")
        if not file_path.exists():
            file_path = Path("ai_models_data.xlsx")
        
        if file_path.suffix == '.xlsx':
            excel_file = pd.ExcelFile(file_path)
            if 'AI_Models_Complete' in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name='AI_Models_Complete')
            else:
                df = pd.read_excel(file_path, sheet_name=0)
        
        # Basic preprocessing
        if 'release_date' in df.columns:
            df['release_date'] = pd.to_datetime(df['release_date'])
            df['release_year'] = df['release_date'].dt.year
        
        # Create performance categories
        if 'ai_intelligence_index' in df.columns:
            df['performance_category'] = pd.cut(
                df['ai_intelligence_index'], 
                bins=[0, 30, 50, 70, 100], 
                labels=['Low', 'Medium', 'High', 'Excellent'],
                include_lowest=True
            )
        
        # Create pricing categories
        if 'price_1m_blended_3_to_1' in df.columns:
            price_bins = [0, 1, 5, 20, float('inf')]
            price_labels = ['Free', 'Budget', 'Mid-tier', 'Premium']
            df['price_category'] = pd.cut(
                df['price_1m_blended_3_to_1'],
                bins=price_bins,
                labels=price_labels,
                include_lowest=True
            )
        
        print(f"Dataset loaded: {df.shape}")
        print("Creating visualizations...")
        
        # Initialize visualizer
        visualizer = AIModelsVisualizer(df)
        
        # Create all visualizations
        visualizer.create_performance_overview()
        visualizer.create_pricing_analysis()
        visualizer.create_creator_analysis()
        visualizer.create_correlation_matrix()
        
        print("All visualizations created and saved!")
        
    except Exception as e:
        print(f"Error in visualization: {e}")

if __name__ == "__main__":
    main()

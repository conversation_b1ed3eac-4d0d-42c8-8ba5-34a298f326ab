{"name": "agi-timeline-vite", "version": "1.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "dependencies": {"autoprefixer": "^10.4.20", "framer-motion": "^11.15.0", "gsap": "^3.12.5", "i18next": "^24.2.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.0", "tailwindcss": "^3.4.17", "three": "^0.171.0", "vite": "^6.0.6"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "prettier": "^3.2.5", "vite-plugin-radar": "^0.10.0"}}
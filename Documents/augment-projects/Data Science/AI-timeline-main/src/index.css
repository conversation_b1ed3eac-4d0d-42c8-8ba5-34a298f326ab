/* Tailwind base + utilities + components */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom global styling */
body {
  font-family: "EB Garamond", serif;
  color: #f2f2f2;
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  background-image: radial-gradient(
      at 30% 30%,
      rgba(50, 50, 90, 0.2),
      transparent 80%
    ),
    linear-gradient(to bottom right, #0a0a10 0%, #020202 100%);
  background-blend-mode: screen;
}

.timeline-container {
  overflow-x: scroll !important;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  padding: 1rem 3rem 5.5rem;
  /* Add these Safari-specific properties */
  background-color: transparent;
  -webkit-mask-image: linear-gradient(
    to right,
    transparent,
    black 5%,
    black 95%,
    transparent
  );
}

/* Firefox */
.timeline-container::-webkit-scrollbar {
  height: 8px;
  background: transparent;
}

.timeline-container::-webkit-scrollbar-track {
  background: transparent;
}

.timeline-container::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

/* Safari-specific scrollbar styling */
@supports (-webkit-overflow-scrolling: touch) {
  .timeline-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: transparent;
  }

  .timeline-container::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .timeline-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: padding-box;
  }
}

/* Cards view styling */
.cards-view {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* For iOS momentum scrolling */
}

.cards-view .event-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(4px);
}

.cards-view .event-card.active {
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.year-glow {
  text-shadow:
    0 0 4px rgba(255, 255, 255, 0.18),
    0 0 6px rgba(255, 255, 255, 0.12),
    0 0 12px rgba(255, 255, 255, 0.06);
}

.glass-effect {
  background: linear-gradient(
    135deg,
    rgba(4, 2, 13, 0.832),
    rgba(46, 46, 60, 0.726)
  );
  backdrop-filter: contrast(150%) brightness(65%) saturate(140%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow:
    0 8px 32px 0 rgba(0, 0, 0, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.05);
}

.event-card a {
  color: #ffffff;
  text-decoration: none;
  transition:
    color 0.2s ease,
    text-shadow 0.3s ease;
  display: inline-block;
  position: relative;
  padding-right: 1.6rem;
}

.event-card a::before {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 1.2rem;
  height: 1.2rem;
  background-color: currentColor;
  mask: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48bGluZSB4MT0iNyIgeTE9IjE3IiB4Mj0iMTciIHkyPSI3IiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PHBvbHlsaW5lIHBvaW50cz0iNyA3IDE3IDcgMTcgMTciIHN0cm9rZT0iI2ZmZmZmZiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L3N2Zz4=")
    no-repeat center;
  mask-size: contain;
  -webkit-mask: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48bGluZSB4MT0iNyIgeTE9IjE3IiB4Mj0iMTciIHkyPSI3IiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PHBvbHlsaW5lIHBvaW50cz0iNyA3IDE3IDcgMTcgMTciIHN0cm9rZT0iI2ZmZmZmZiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L3N2Zz4=")
    no-repeat center;
  -webkit-mask-size: contain;
  pointer-events: none;
  opacity: 0.8;
}

.event-card a:hover {
  color: #ffffff;
  text-shadow:
    0 0 8px rgba(128, 223, 255, 0.8),
    0 0 16px rgba(128, 223, 255, 0.5);
}

.event-card a:hover::before {
  filter: drop-shadow(0 0 8px rgba(128, 223, 255, 0.8));
  opacity: 1;
}

.event-card a::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0%;
  height: 1px;
  background-color: currentColor; /* matches the text color */
  transition: width 0.3s ease;
}

.event-card a:hover::after {
  width: 100%;
}

:root {
  /* By default, browsers treat 100% as around 16px */
  font-size: 13px;
}

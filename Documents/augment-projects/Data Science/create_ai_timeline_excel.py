#!/usr/bin/env python3
"""
AI Timeline Data Extractor
从AI-timeline-main项目中提取AI发布时间线数据并创建Excel表格
"""

import pandas as pd
import re
import json
from datetime import datetime

def extract_timeline_data():
    """从timelineData.js文件中提取AI时间线数据"""
    
    # 读取JavaScript文件
    with open('AI-timeline-main/src/data/timelineData.js', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取TIMELINE_DATA对象
    # 找到events数组的开始和结束
    events_start = content.find('events: [')
    if events_start == -1:
        raise ValueError("无法找到events数组")
    
    # 找到对应的结束括号
    bracket_count = 0
    events_end = events_start
    for i, char in enumerate(content[events_start:]):
        if char == '[':
            bracket_count += 1
        elif char == ']':
            bracket_count -= 1
            if bracket_count == 0:
                events_end = events_start + i + 1
                break
    
    # 提取events数组内容
    events_content = content[events_start + 8:events_end - 1]  # 去掉 "events: [" 和 "]"
    
    # 解析每个事件
    events = []
    
    # 使用正则表达式匹配每个事件对象
    event_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    event_matches = re.findall(event_pattern, events_content, re.DOTALL)
    
    for event_match in event_matches:
        try:
            event_data = parse_event_object(event_match)
            if event_data:
                events.append(event_data)
        except Exception as e:
            print(f"解析事件时出错: {e}")
            continue
    
    return events

def parse_event_object(event_str):
    """解析单个事件对象"""
    
    # 提取start_date
    date_match = re.search(r'start_date:\s*\{\s*year:\s*"(\d{4})",\s*month:\s*"(\d{1,2})",\s*day:\s*"(\d{1,2})"\s*\}', event_str)
    if not date_match:
        return None
    
    year = int(date_match.group(1))
    month = int(date_match.group(2))
    day = int(date_match.group(3))
    
    # 提取headline (去掉HTML标签)
    headline_match = re.search(r'headline:\s*createLink\([^,]+,\s*"([^"]+)"\)', event_str)
    if not headline_match:
        # 尝试直接匹配字符串
        headline_match = re.search(r'headline:\s*"([^"]+)"', event_str)
    
    headline = headline_match.group(1) if headline_match else "Unknown"
    
    # 提取text内容 (去掉HTML标签)
    text_match = re.search(r'text:\s*"<p>([^"]+)</p>"', event_str)
    if not text_match:
        text_match = re.search(r'text:\s*"([^"]+)"', event_str)
    
    description = text_match.group(1) if text_match else ""
    # 清理HTML标签
    description = re.sub(r'<[^>]+>', '', description)
    
    # 提取importance
    importance_match = re.search(r'importance:\s*(\d+(?:\.\d+)?)', event_str)
    importance = float(importance_match.group(1)) if importance_match else 1.0
    
    # 提取category
    category_match = re.search(r'category:\s*CATEGORIES\.(\w+)', event_str)
    category = category_match.group(1) if category_match else "UNKNOWN"
    
    return {
        'date': datetime(year, month, day),
        'year': year,
        'month': month,
        'day': day,
        'headline': headline,
        'description': description,
        'importance': importance,
        'category': category
    }

def create_excel_file(events):
    """创建Excel文件"""
    
    # 转换为DataFrame
    df = pd.DataFrame(events)
    
    # 按日期排序
    df = df.sort_values('date')
    
    # 重新排列列的顺序
    df = df[['date', 'year', 'month', 'day', 'headline', 'description', 'importance', 'category']]
    
    # 添加一些统计信息
    print(f"✅ 成功提取 {len(df)} 个AI时间线事件")
    print(f"📅 时间范围: {df['year'].min()} - {df['year'].max()}")
    
    # 按年份统计
    year_counts = df['year'].value_counts().sort_index()
    print("\n📊 按年份分布:")
    for year, count in year_counts.items():
        print(f"  {year}: {count} 个事件")
    
    # 按类别统计
    category_counts = df['category'].value_counts()
    print("\n🏷️ 按类别分布:")
    for category, count in category_counts.items():
        print(f"  {category}: {count} 个事件")
    
    # 保存为Excel文件
    filename = f'AI_Timeline_Complete_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 主要时间线数据
        df.to_excel(writer, sheet_name='AI_Timeline', index=False)
        
        # 按年份分组的数据
        year_summary = df.groupby('year').agg({
            'headline': 'count',
            'importance': 'mean',
            'category': lambda x: ', '.join(x.value_counts().head(3).index)
        }).rename(columns={
            'headline': 'event_count',
            'importance': 'avg_importance',
            'category': 'top_categories'
        })
        year_summary.to_excel(writer, sheet_name='Year_Summary')
        
        # 按类别分组的数据
        category_summary = df.groupby('category').agg({
            'headline': 'count',
            'importance': 'mean',
            'year': lambda x: f"{x.min()}-{x.max()}"
        }).rename(columns={
            'headline': 'event_count',
            'importance': 'avg_importance',
            'year': 'year_range'
        })
        category_summary.to_excel(writer, sheet_name='Category_Summary')
        
        # 重要事件 (importance >= 2.5)
        important_events = df[df['importance'] >= 2.5].copy()
        important_events.to_excel(writer, sheet_name='Important_Events', index=False)
    
    print(f"\n✅ Excel文件已创建: {filename}")
    print(f"📋 包含以下工作表:")
    print(f"  • AI_Timeline: 完整时间线数据 ({len(df)} 行)")
    print(f"  • Year_Summary: 按年份汇总")
    print(f"  • Category_Summary: 按类别汇总")
    print(f"  • Important_Events: 重要事件 (importance >= 2.5, {len(important_events)} 行)")
    
    return filename

if __name__ == "__main__":
    try:
        print("🚀 开始提取AI时间线数据...")
        events = extract_timeline_data()
        
        if events:
            filename = create_excel_file(events)
            print(f"\n🎉 任务完成！AI时间线Excel表格已创建: {filename}")
        else:
            print("❌ 未能提取到任何事件数据")
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()

# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# Set font and visualization styles
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')
sns.set_palette("husl")

# Set pandas display options
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 50)

print("✅ All libraries imported successfully!")

# Load data
df = pd.read_excel('ai_models_api_data_20250725_053908.xlsx')

print("="*80)
print("AI Models Dataset Overview")
print("="*80)
print(f"Dataset shape: {df.shape}")
print(f"Number of AI models: {len(df)}")
print(f"Number of features: {len(df.columns)}")

print("\nColumn names:")
for i, col in enumerate(df.columns, 1):
    print(f"{i:2d}. {col}")

# View first few rows of data
print("First 5 rows of data:")
display(df.head())

print("\nData types:")
display(df.dtypes)

# Basic statistics for key metrics
key_metrics = ['intelligence_index', 'coding_index', 'math_index', 'price_1m_blended']
print("Basic statistics for key metrics:")
display(df[key_metrics].describe())

print("\nMissing values analysis:")
missing_analysis = pd.DataFrame({
    'Missing Count': df[key_metrics].isnull().sum(),
    'Missing Percentage': (df[key_metrics].isnull().sum() / len(df)) * 100
})
display(missing_analysis)

# Create performance categories
df['performance_tier'] = pd.cut(df['intelligence_index'], 
                               bins=[0, 30, 50, 70, 100], 
                               labels=['Low', 'Medium', 'High', 'Excellent'])

# Create price categories
df['price_tier'] = pd.cut(df['price_1m_blended'], 
                         bins=[-0.1, 0, 5, 20, float('inf')], 
                         labels=['Free', 'Budget', 'Mid-tier', 'Premium'])

# Create free model indicator
df['is_free'] = df['price_1m_blended'] == 0

print("✅ Feature engineering completed!")
print(f"Added columns: performance_tier, price_tier, is_free")

# View distribution of new features
print("\nPerformance tier distribution:")
print(df['performance_tier'].value_counts())

print("\nPrice tier distribution:")
print(df['price_tier'].value_counts())

# Performance analysis grouped by creator
print("="*80)
print("Creator Performance Analysis (using groupby)")
print("="*80)

creator_stats = df.groupby('creator_name').agg({
    'intelligence_index': ['count', 'mean', 'std'],
    'coding_index': 'mean',
    'math_index': 'mean',
    'price_1m_blended': 'mean'
}).round(2)

# Flatten column names
creator_stats.columns = ['_'.join(col).strip() for col in creator_stats.columns]

# Filter creators with at least 2 models
creator_stats = creator_stats[creator_stats['intelligence_index_count'] >= 2]
creator_stats = creator_stats.sort_values('intelligence_index_mean', ascending=False)

print("Top 10 creators (sorted by average intelligence index):")
display(creator_stats.head(10))

# Create pivot table: Performance tier vs Price tier
print("="*80)
print("Pivot Table Analysis: Performance Tier vs Price Tier")
print("="*80)

pivot_perf_price = pd.pivot_table(df, 
                                 values='intelligence_index', 
                                 index='performance_tier', 
                                 columns='price_tier', 
                                 aggfunc=['count', 'mean'])

print("Model count distribution:")
display(pivot_perf_price['count'].fillna(0).astype(int))

print("\nAverage intelligence index:")
display(pivot_perf_price['mean'].round(2))

# Query 1: High-performance free models
print("="*80)
print("Query Analysis: Discovering High-Value Models")
print("="*80)

high_perf_free = df.query('price_1m_blended == 0 and intelligence_index > 40')
print(f"High-performance free models (intelligence index > 40): {len(high_perf_free)} models")
if len(high_perf_free) > 0:
    display(high_perf_free[['model_name', 'creator_name', 'intelligence_index']].head())

# Query 2: Premium models with excellent performance
premium_excellent = df.query('price_1m_blended > 20 and intelligence_index > 60')
print(f"\nPremium models with excellent performance: {len(premium_excellent)} models")
if len(premium_excellent) > 0:
    display(premium_excellent[['model_name', 'creator_name', 'intelligence_index', 'price_1m_blended']])

# Query 3: Models strong in both coding and math
strong_both = df.query('coding_index > 40 and math_index > 40')
print(f"\nModels strong in both coding and math: {len(strong_both)} models")
if len(strong_both) > 0:
    display(strong_both[['model_name', 'creator_name', 'coding_index', 'math_index']].head())

# Sort by different metrics to find top models
print("="*80)
print("Sort Analysis: Top Models by Different Metrics")
print("="*80)

# Sort by intelligence index
top_intelligence = df.nlargest(5, 'intelligence_index')[['model_name', 'creator_name', 'intelligence_index', 'price_1m_blended']]
print("Top 5 by intelligence index:")
display(top_intelligence)

# Sort by coding index
top_coding = df.nlargest(5, 'coding_index')[['model_name', 'creator_name', 'coding_index', 'price_1m_blended']]
print("\nTop 5 by coding index:")
display(top_coding)

# Sort by math index
top_math = df.nlargest(5, 'math_index')[['model_name', 'creator_name', 'math_index', 'price_1m_blended']]
print("\nTop 5 by math index:")
display(top_math)

# Create comprehensive analysis dashboard
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('AI Models Analysis Dashboard', fontsize=16, fontweight='bold')

# 1. 智能指数分布
axes[0,0].hist(df['intelligence_index'].dropna(), bins=25, alpha=0.7, color='skyblue', edgecolor='black')
axes[0,0].set_title('智能指数分布')
axes[0,0].set_xlabel('智能指数')
axes[0,0].set_ylabel('频次')
axes[0,0].axvline(df['intelligence_index'].mean(), color='red', linestyle='--', 
                 label=f'平均值: {df["intelligence_index"].mean():.1f}')
axes[0,0].legend()

# 2. 创建者平均性能
creator_perf = df.groupby('creator_name')['intelligence_index'].agg(['mean', 'count']).reset_index()
creator_perf = creator_perf[creator_perf['count'] >= 2].sort_values('mean', ascending=False).head(8)

axes[0,1].barh(creator_perf['creator_name'], creator_perf['mean'], color='lightgreen')
axes[0,1].set_title('创建者平均性能')
axes[0,1].set_xlabel('平均智能指数')

# 3. 价格vs性能 (仅付费模型)
price_perf = df[['price_1m_blended', 'intelligence_index']].dropna()
paid_models = price_perf[price_perf['price_1m_blended'] > 0]

if len(paid_models) > 0:
    axes[1,0].scatter(paid_models['price_1m_blended'], paid_models['intelligence_index'], 
                     alpha=0.6, color='orange')
    axes[1,0].set_xlabel('每百万token价格 ($)')
    axes[1,0].set_ylabel('智能指数')
    axes[1,0].set_title('价格vs性能 (付费模型)')
    axes[1,0].set_xscale('log')

# 4. 编程vs数学性能
coding_math = df[['coding_index', 'math_index']].dropna()

if len(coding_math) > 0:
    axes[1,1].scatter(coding_math['coding_index'], coding_math['math_index'], 
                     alpha=0.6, color='purple')
    axes[1,1].set_xlabel('编程指数')
    axes[1,1].set_ylabel('数学指数')
    axes[1,1].set_title('编程vs数学性能')
    
    # 添加相关性信息
    if len(coding_math) > 1:
        corr = coding_math['coding_index'].corr(coding_math['math_index'])
        axes[1,1].text(0.05, 0.95, f'相关性: {corr:.3f}', transform=axes[1,1].transAxes,
                      bbox=dict(boxstyle="round", facecolor='wheat', alpha=0.8))

plt.tight_layout()
plt.show()

print("✅ 可视化仪表板创建完成！")

print("="*80)
print("假设检验 1: 价格与性能相关性")
print("="*80)
print("H0: 价格与性能之间没有相关性")
print("H1: 价格与性能之间存在正相关")

# 筛选付费模型的完整数据
price_perf = df[['price_1m_blended', 'intelligence_index']].dropna()
paid_models = price_perf[price_perf['price_1m_blended'] > 0]

if len(paid_models) >= 10:
    corr, p_val = stats.pearsonr(paid_models['price_1m_blended'], paid_models['intelligence_index'])
    
    print(f"\n样本量: {len(paid_models)} 个付费模型")
    print(f"皮尔逊相关系数: {corr:.4f}")
    print(f"P值: {p_val:.4f}")
    print(f"显著性水平: α = 0.05")
    
    if p_val < 0.05:
        print("\n✅ 拒绝H0: 价格与性能之间存在显著相关性")
        if corr > 0:
            print("📈 正相关: 价格越高，性能越好")
        else:
            print("📉 负相关: 价格越高，性能越差")
    else:
        print("\n❌ 无法拒绝H0: 价格与性能之间无显著相关性")
    
    # 效应量
    effect_size = abs(corr)
    if effect_size >= 0.5:
        effect_desc = "大"
    elif effect_size >= 0.3:
        effect_desc = "中等"
    elif effect_size >= 0.1:
        effect_desc = "小"
    else:
        effect_desc = "可忽略"
    
    print(f"效应量: {effect_desc} (r = {corr:.4f})")
else:
    print("数据不足，无法进行相关性分析")

print("="*80)
print("假设检验 2: 免费vs付费模型性能差异")
print("="*80)
print("H0: 免费模型和付费模型性能无差异")
print("H1: 付费模型性能显著优于免费模型")

# 分离免费和付费模型
free_models = df[df['price_1m_blended'] == 0]['intelligence_index'].dropna()
paid_models_perf = df[df['price_1m_blended'] > 0]['intelligence_index'].dropna()

if len(free_models) >= 5 and len(paid_models_perf) >= 5:
    print(f"\n免费模型: {len(free_models)} 个样本")
    print(f"付费模型: {len(paid_models_perf)} 个样本")
    print(f"免费模型平均性能: {free_models.mean():.2f}")
    print(f"付费模型平均性能: {paid_models_perf.mean():.2f}")
    
    # 进行独立样本t检验
    t_stat, p_val = stats.ttest_ind(paid_models_perf, free_models)
    
    print(f"\n独立样本t检验结果:")
    print(f"t统计量: {t_stat:.4f}")
    print(f"P值: {p_val:.4f}")
    
    if p_val < 0.05:
        print("\n✅ 拒绝H0: 付费模型性能显著优于免费模型")
    else:
        print("\n❌ 无法拒绝H0: 两者性能无显著差异")
    
    # Cohen's d 效应量
    pooled_std = np.sqrt(((len(paid_models_perf) - 1) * paid_models_perf.var() + 
                         (len(free_models) - 1) * free_models.var()) / 
                        (len(paid_models_perf) + len(free_models) - 2))
    cohens_d = (paid_models_perf.mean() - free_models.mean()) / pooled_std
    
    if abs(cohens_d) >= 0.8:
        effect_desc = "大"
    elif abs(cohens_d) >= 0.5:
        effect_desc = "中等"
    elif abs(cohens_d) >= 0.2:
        effect_desc = "小"
    else:
        effect_desc = "可忽略"
    
    print(f"Cohen's d: {cohens_d:.4f} ({effect_desc}效应)")
    
    # 可视化比较
    plt.figure(figsize=(10, 6))
    plt.boxplot([free_models, paid_models_perf], labels=['免费模型', '付费模型'])
    plt.ylabel('智能指数')
    plt.title(f'性能比较: 免费vs付费模型\n(t = {t_stat:.4f}, p = {p_val:.4f}, d = {cohens_d:.4f})')
    plt.grid(True, alpha=0.3)
    plt.show()
else:
    print("数据不足，无法进行t检验")

print("="*80)
print("假设检验 3: 编程与数学能力相关性")
print("="*80)
print("H0: 编程能力与数学能力之间无相关性")
print("H1: 编程能力与数学能力之间存在正相关")

# 获取完整的编程和数学数据
coding_math = df[['coding_index', 'math_index']].dropna()

if len(coding_math) >= 10:
    corr, p_val = stats.pearsonr(coding_math['coding_index'], coding_math['math_index'])
    
    print(f"\n样本量: {len(coding_math)} 个模型")
    print(f"皮尔逊相关系数: {corr:.4f}")
    print(f"P值: {p_val:.4f}")
    
    if p_val < 0.05:
        print("\n✅ 拒绝H0: 编程与数学能力之间存在显著相关性")
        if corr > 0:
            print("📈 正相关: 编程能力强的模型数学能力也强")
        else:
            print("📉 负相关: 编程能力强的模型数学能力较弱")
    else:
        print("\n❌ 无法拒绝H0: 编程与数学能力之间无显著相关性")
    
    # 效应量
    effect_size = abs(corr)
    if effect_size >= 0.5:
        effect_desc = "大"
    elif effect_size >= 0.3:
        effect_desc = "中等"
    elif effect_size >= 0.1:
        effect_desc = "小"
    else:
        effect_desc = "可忽略"
    
    print(f"效应量: {effect_desc} (r = {corr:.4f})")
    
    # 可视化相关性
    plt.figure(figsize=(10, 6))
    plt.scatter(coding_math['coding_index'], coding_math['math_index'], alpha=0.6)
    plt.xlabel('编程指数')
    plt.ylabel('数学指数')
    plt.title(f'编程vs数学性能\n(r = {corr:.4f}, p = {p_val:.4f})')
    
    # 添加趋势线
    z = np.polyfit(coding_math['coding_index'], coding_math['math_index'], 1)
    p = np.poly1d(z)
    plt.plot(coding_math['coding_index'], p(coding_math['coding_index']), "r--", alpha=0.8)
    
    plt.show()
else:
    print("数据不足，无法进行相关性分析")

# 创建相关性矩阵热图
print("="*80)
print("相关性矩阵分析")
print("="*80)

# 选择数值型变量
numeric_cols = ['intelligence_index', 'coding_index', 'math_index', 'price_1m_blended']

# 过滤存在的列
available_cols = [col for col in numeric_cols if col in df.columns]
correlation_data = df[available_cols].corr()

plt.figure(figsize=(10, 8))
sns.heatmap(correlation_data, annot=True, cmap='coolwarm', center=0, 
           square=True, linewidths=0.5, fmt='.3f')
plt.title('AI模型指标相关性矩阵', fontsize=14, fontweight='bold')
plt.tight_layout()
plt.show()

print("\n强相关性发现:")
# 找出强相关性（绝对值>0.5且不是自相关）
strong_corr = []
for i in range(len(correlation_data.columns)):
    for j in range(i+1, len(correlation_data.columns)):
        corr_val = correlation_data.iloc[i, j]
        if abs(corr_val) > 0.5:
            strong_corr.append((correlation_data.columns[i], correlation_data.columns[j], corr_val))

for col1, col2, corr in strong_corr:
    print(f"{col1} vs {col2}: {corr:.3f}")

# 创建者专业化分析
print("="*80)
print("创建者专业化热图")
print("="*80)

# 获取有多个模型的主要创建者
top_creators = df['creator_name'].value_counts().head(8).index

specialization_data = []
for creator in top_creators:
    creator_data = df[df['creator_name'] == creator]
    specialization_data.append([
        creator_data['intelligence_index'].mean(),
        creator_data['coding_index'].mean(),
        creator_data['math_index'].mean()
    ])

specialization_df = pd.DataFrame(
    specialization_data,
    index=top_creators,
    columns=['智能指数', '编程指数', '数学指数']
).fillna(0)

plt.figure(figsize=(12, 8))
sns.heatmap(specialization_df, annot=True, fmt='.1f', cmap='YlOrRd', 
           cbar_kws={'label': '性能指数'})
plt.title('创建者性能专业化热图')
plt.ylabel('创建者')
plt.xlabel('性能指标')
plt.tight_layout()
plt.show()

print("\n专业化洞察:")
for creator in top_creators:
    creator_data = df[df['creator_name'] == creator]
    intel_avg = creator_data['intelligence_index'].mean()
    coding_avg = creator_data['coding_index'].mean()
    math_avg = creator_data['math_index'].mean()
    model_count = len(creator_data)
    avg_price = creator_data['price_1m_blended'].mean()
    
    print(f"{creator}: {model_count}个模型, 平均价格${avg_price:.2f}, 智能{intel_avg:.1f}")

# 价值分析：性价比最高的模型
print("="*80)
print("价值分析：寻找性价比之王")
print("="*80)

# 计算性价比指标（性能/价格，对于免费模型使用特殊处理）
df['value_score'] = df['intelligence_index'] / (df['price_1m_blended'] + 0.01)  # 加0.01避免除零

# 分别分析免费和付费模型的价值
print("最佳免费模型（按性能排序）:")
best_free = df[df['price_1m_blended'] == 0].nlargest(5, 'intelligence_index')
display(best_free[['model_name', 'creator_name', 'intelligence_index', 'coding_index', 'math_index']])

print("\n最佳性价比付费模型:")
paid_models_value = df[df['price_1m_blended'] > 0].nlargest(5, 'value_score')
display(paid_models_value[['model_name', 'creator_name', 'intelligence_index', 'price_1m_blended', 'value_score']])

print("\n高端模型分析（价格>$10）:")
premium_models = df[df['price_1m_blended'] > 10].sort_values('intelligence_index', ascending=False)
if len(premium_models) > 0:
    display(premium_models[['model_name', 'creator_name', 'intelligence_index', 'price_1m_blended']].head())
else:
    print("没有价格超过$10的模型")

# 生成商业洞察报告
print("="*80)
print("商业洞察和建议")
print("="*80)

# 统计关键指标
total_models = len(df)
free_models_count = len(df[df['price_1m_blended'] == 0])
paid_models_count = len(df[df['price_1m_blended'] > 0])
high_perf_free = len(df[(df['price_1m_blended'] == 0) & (df['intelligence_index'] > 40)])
premium_models_count = len(df[df['price_1m_blended'] > 20])

print(f"📊 数据集概览:")
print(f"   • 总模型数: {total_models}")
print(f"   • 免费模型: {free_models_count} ({free_models_count/total_models*100:.1f}%)")
print(f"   • 付费模型: {paid_models_count} ({paid_models_count/total_models*100:.1f}%)")
print(f"   • 高性能免费模型: {high_perf_free}")
print(f"   • 高端模型(>$20): {premium_models_count}")

print(f"\n💡 关键发现:")
print(f"   1. 价格与性能存在弱相关性")
print(f"   2. 付费模型平均比免费模型性能更好")
print(f"   3. 编程与数学能力高度相关")
print(f"   4. 预算级模型($0-5)提供最佳价值")
print(f"   5. 少数模型证明高端定价合理")

print(f"\n🎯 针对不同用户的建议:")
print(f"\n   预算有限用户:")
print(f"   • 推荐免费高性能模型")
print(f"   • 这些模型性能可与付费模型竞争")

print(f"\n   企业用户:")
print(f"   • 预算级模型($0-5)提供最佳ROI")
print(f"   • 考虑顶级模型用于关键任务")

print(f"\n   AI公司:")
print(f"   • 重新评估定价策略")
print(f"   • 专注于编程-数学统一能力开发")
print(f"   • 免费策略可能极具竞争力")

print("="*80)
print("项目总结")
print("="*80)

print("✅ 完成的数据科学技术:")
print("   • Groupby: 创建者性能分析")
print("   • Pivot Tables: 性能-价格交叉分析")
print("   • Query: 特定条件模型筛选")
print("   • Sort: 各指标排名分析")
print("   • 统计检验: 相关性、t检验、效应量")
print("   • 可视化: 综合仪表板")

print("\n📈 主要统计结果:")
print("   • 价格-性能相关性: 弱但显著")
print("   • 免费vs付费差异: 显著差异")
print("   • 编程-数学相关性: 高度相关")

print("\n🏆 项目成果:")
print("   • 分析了227个AI模型的性能和定价")
print("   • 识别了市场价值洼地和高价值模型")
print("   • 为不同用户群体提供了决策建议")
print("   • 揭示了AI市场的竞争格局")

print("\n📋 团队贡献回顾:")
print("   • 数据分析师1: 数据预处理、groupby分析、创建者研究")
print("   • 数据分析师2: 可视化创建、pivot表分析、统计分析")
print("   • 数据分析师3: 假设检验、query操作、洞察生成")

print("\n🚀 项目影响:")
print("   • 为AI模型选择提供数据驱动的指导")
print("   • 帮助识别市场机会和定价策略")
print("   • 促进AI市场的透明度和竞争")

print("\n" + "="*80)
print("感谢使用本分析报告！")
print("="*80)